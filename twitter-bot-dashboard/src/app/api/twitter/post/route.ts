import { NextRequest, NextResponse } from 'next/server'
import { TwitterApi } from 'twitter-api-v2'
import { dbOperations } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { content } = await request.json()
    
    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json(
        { error: 'Tweet content is required' },
        { status: 400 }
      )
    }

    // Validate tweet length (Twitter's limit is 280 characters)
    if (content.length > 280) {
      return NextResponse.json(
        { error: 'Tweet content exceeds 280 character limit' },
        { status: 400 }
      )
    }

    // Initialize Twitter client with OAuth2 credentials
    const client = new TwitterApi({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
    } as any)

    // Check if we have a refresh token
    if (!process.env.TWITTER_REFRESH_TOKEN) {
      return NextResponse.json(
        { error: 'Twitter authentication required. Please complete OAuth flow first.' },
        { status: 401 }
      )
    }

    try {
      // Refresh the OAuth2 token to get a fresh access token
      const { client: rwClient, accessToken, refreshToken } = await client.refreshOAuth2Token(
        process.env.TWITTER_REFRESH_TOKEN
      )

      // Post the tweet using the refreshed client
      const tweet = await rwClient.v2.tweet(content)

      // Save the posted tweet to database for analytics
      await dbOperations.addPostedTweet({
        tweet_id: tweet.data.id,
        content: content,
        original_url: undefined, // Direct tweet, no original URL
        original_title: undefined, // Direct tweet, no original title
        impressions: 0,
        retweets: 0,
        likes: 0,
        replies: 0,
        posted_at: new Date().toISOString()
      })

      // Log successful post
      console.log('✅ Tweet posted successfully:', {
        id: tweet.data.id,
        text: content,
        length: content.length
      })

      return NextResponse.json({
        success: true,
        tweet: {
          id: tweet.data.id,
          text: content,
          created_at: new Date().toISOString(),
          url: `https://twitter.com/i/web/status/${tweet.data.id}`
        }
      })

    } catch (twitterError: any) {
      console.error('Twitter API Error:', twitterError)
      
      // Handle specific Twitter API errors
      if (twitterError.code === 401) {
        return NextResponse.json(
          { error: 'Twitter authentication expired. Please re-authenticate.' },
          { status: 401 }
        )
      }
      
      if (twitterError.code === 403) {
        return NextResponse.json(
          { error: 'Twitter API access forbidden. Check your app permissions.' },
          { status: 403 }
        )
      }
      
      if (twitterError.code === 429) {
        return NextResponse.json(
          { error: 'Twitter API rate limit exceeded. Please try again later.' },
          { status: 429 }
        )
      }

      // Handle duplicate tweet error
      if (twitterError.data?.detail?.includes('duplicate')) {
        return NextResponse.json(
          { error: 'This tweet appears to be a duplicate of a recent tweet.' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: `Twitter API error: ${twitterError.message || 'Unknown error'}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error posting tweet:', error)
    return NextResponse.json(
      { error: 'Failed to post tweet. Please try again.' },
      { status: 500 }
    )
  }
}
