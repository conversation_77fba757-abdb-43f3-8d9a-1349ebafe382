# Twitter Post API

This document describes the new Twitter Post API endpoint that allows posting individual tweets directly from the UI.

## API Endpoint

**POST** `/api/twitter/post`

Posts a single tweet to Twitter using OAuth2 authentication with automatic token refresh.

### Request

#### Headers
```
Content-Type: application/json
```

#### Body
```json
{
  "content": "Your tweet content here"
}
```

#### Parameters
- `content` (string, required): The tweet content (max 280 characters)

### Response

#### Success Response (200)
```json
{
  "success": true,
  "tweet": {
    "id": "1234567890123456789",
    "text": "Your tweet content here",
    "created_at": "2024-01-01T12:00:00.000Z",
    "url": "https://twitter.com/i/web/status/1234567890123456789"
  }
}
```

#### Error Responses

**400 Bad Request** - Invalid input
```json
{
  "error": "Tweet content is required"
}
```

**401 Unauthorized** - Authentication required
```json
{
  "error": "Twitter authentication required. Please complete OAuth flow first."
}
```

**429 Too Many Requests** - Rate limit exceeded
```json
{
  "error": "Twitter API rate limit exceeded. Please try again later."
}
```

**500 Internal Server Error** - Server error
```json
{
  "error": "Failed to post tweet. Please try again."
}
```

## Features

### 🔄 Automatic Token Refresh
- Uses OAuth2 refresh tokens to automatically obtain fresh access tokens
- Based on the working `auth.js` implementation
- Handles token expiration gracefully

### ✅ Input Validation
- Validates tweet content is provided and not empty
- Enforces Twitter's 280 character limit
- Provides specific error messages for different validation failures

### 🛡️ Error Handling
- Comprehensive error handling for Twitter API responses
- Specific error messages for common issues (auth, rate limits, duplicates)
- Graceful fallback for unexpected errors

### 📊 Database Integration
- Automatically saves posted tweets to the database
- Integrates with existing analytics system
- Tracks tweet metadata for reporting

### 🔍 Detailed Logging
- Logs successful posts with tweet details
- Error logging for debugging
- Character count and content validation logging

## Environment Variables Required

```bash
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_REFRESH_TOKEN=your_refresh_token
```

## Usage Examples

### JavaScript/TypeScript (Frontend)
```typescript
async function postTweet(content: string) {
  try {
    const response = await fetch('/api/twitter/post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ content }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('Tweet posted:', data.tweet);
      return data.tweet;
    } else {
      console.error('Error:', data.error);
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('Network error:', error);
    throw error;
  }
}

// Usage
postTweet("Hello, Twitter! 🚀")
  .then(tweet => console.log('Success:', tweet))
  .catch(error => console.error('Failed:', error));
```

### cURL
```bash
curl -X POST http://localhost:3000/api/twitter/post \
  -H "Content-Type: application/json" \
  -d '{"content": "Hello from the API! 🚀"}'
```

### Node.js Test Script
```bash
node test-tweet-api.js "Your tweet content here"
```

## React Component

A complete React component (`TweetComposer`) is provided that includes:
- Character count with visual feedback
- Form validation
- Loading states
- Success/error message display
- Responsive design with Tailwind CSS

Access it at: `/compose`

## Integration with Existing System

This API endpoint integrates seamlessly with the existing Twitter bot dashboard:

1. **Uses existing database operations** from `@/lib/supabase`
2. **Follows the same authentication pattern** as other Twitter operations
3. **Maintains consistency** with existing API structure
4. **Leverages the same error handling patterns**

## Differences from Content Queue API

| Feature | `/api/content/post` | `/api/twitter/post` |
|---------|-------------------|-------------------|
| Purpose | Post queued content items | Post individual tweets |
| Input | Array of content IDs | Single tweet content |
| Source | RSS feeds + AI generation | Direct user input |
| Batch | Multiple tweets | Single tweet |
| Delay | 2s between posts | Immediate |

## Security Considerations

- ✅ Input validation and sanitization
- ✅ Rate limiting awareness
- ✅ Secure token handling
- ✅ Error message sanitization
- ✅ No sensitive data in responses

## Testing

1. **Unit Testing**: Test the API endpoint directly
2. **Integration Testing**: Test with the React component
3. **Manual Testing**: Use the provided test script

```bash
# Test the API endpoint
node test-tweet-api.js "Test tweet content"

# Test the UI component
# Navigate to http://localhost:3000/compose
```

## Troubleshooting

### Common Issues

1. **"Twitter authentication required"**
   - Ensure `TWITTER_REFRESH_TOKEN` is set in environment variables
   - Complete the OAuth flow using the `auth.js` script

2. **"Rate limit exceeded"**
   - Wait before posting again
   - Twitter has strict rate limits for posting

3. **"Duplicate tweet"**
   - Twitter prevents posting identical content
   - Modify the tweet content slightly

4. **Network errors**
   - Check internet connection
   - Verify Twitter API status

### Debug Mode

Enable detailed logging by setting:
```bash
NODE_ENV=development
```

This will provide more detailed error messages and request/response logging.
