// auth.mjs
import express from 'express';
import { Twitter<PERSON><PERSON> } from 'twitter-api-v2';
import dotenv from 'dotenv';
dotenv.config();

const app = express();
const PORT = 3001;

const client = new TwitterApi({
  clientId: process.env.TWITTER_CLIENT_ID,
  clientSecret: process.env.TWITTER_CLIENT_SECRET,
});

const callbackUrl = `http://localhost:${PORT}/callback`;

const { url, codeVerifier, state } = client.generateOAuth2AuthLink(callbackUrl, {
  scope: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
});

console.log('\u2728 Visit this URL to authorize the app:');
console.log(url);

app.get('/callback', async (req, res) => {
  const { state: returnedState, code } = req.query;

  if (state !== returnedState) {
    return res.status(400).send('Invalid state');
  }

  try {
    const { client: userClient, accessToken, refreshToken } = await client.loginWithOAuth2({
      code,
      codeVerifier,
      redirectUri: callbackUrl,
    });

    res.send('✅ Authorization successful! You can close this tab.');

    console.log('\n🎉 Access Token:', accessToken);
    console.log('🔁 Refresh Token:', refreshToken);
  } catch (err) {
    console.error('Error getting tokens:', err);
    res.status(500).send('Failed to get tokens');
  }
});

app.listen(PORT, () => {
  console.log(`\nWaiting for OAuth redirect at http://localhost:${PORT}/callback ...`);
});
